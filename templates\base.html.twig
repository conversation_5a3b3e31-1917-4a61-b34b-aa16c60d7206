<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<meta name='robots' content='index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1'/>
		<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
		<title>JOEL LOCATION en Guadeloupe -
			{% block title %}{% endblock %}
		</title>
		<meta name="google-site-verification" content="j2wXTqK0S2jILfO04BGz0vj1NLuQpCEO98nazjfg5As"/>
		<meta name="google-site-verification" content="Ovl1bjaSIruFHRopNDIYsKQLj3iEzivcO1NQ1lb597A"/>
		<meta name="description" content="Agence de Location de voitures basée à le Moule, en Guadeloupe"/>
		<link
		rel="icon" type="image/x-icon" href="../../images/Joel-Location-new.png"/>

		<!-- Core CSS Framework -->
		<link
		rel="stylesheet" href="{{ asset('assets/css/bootstrap.min.css') }}">

		<!-- Fonts -->
		<link href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700" rel="stylesheet">
		<link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.8.2/css/all.css" integrity="sha384-oS3vJWv+0UjzBfQzYUhtDYW+Pj2yciDJxpsK1OYPAYjqT085Qq/1cq5FLXAZQ7Ay" crossorigin="anonymous">
		<link
		rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css">

		<!-- Core Site Styles -->
		<link rel="stylesheet" href="{{ asset('assets/css/animate.css') }}">
		<link rel="stylesheet" href="{{ asset('assets/css/main.css') }}">
		<link rel="stylesheet" href="{{ asset('assets/css/owl.carousel.css') }}">
		<link rel="stylesheet" href="{{ asset('css/style_site.css') }}">
		<link
		rel="stylesheet" href="{{ asset('css/nav.css') }}">

		<!-- Plugins CSS -->
		<link rel="stylesheet" href="{{ asset('assets/css/barfiller.css') }}">
		<link rel="stylesheet" href="{{ asset('assets/css/slicknav.css') }}">
		<link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.10/css/select2.min.css" rel="stylesheet">
		<link
		href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/4.17.47/css/bootstrap-datetimepicker.min.css" rel="stylesheet">

		<!-- Admin/Dashboard CSS (conditionally loaded) -->
		{% if app.request.attributes.get('_route') starts with 'admin' %}
			<link rel="stylesheet" href="{{ asset('dist/css/adminlte.min.css') }}">
			<link rel="stylesheet" href="{{ asset('plugins/overlayScrollbars/css/OverlayScrollbars.min.css') }}">
			<link rel="stylesheet" href="{{ asset('plugins/daterangepicker/daterangepicker.css') }}">
			<link rel="stylesheet" href="{{ asset('plugins/summernote/summernote-bs4.css') }}">
			<link rel="stylesheet" href="{{ asset('plugins/tempusdominus-bootstrap-4/css/tempusdominus-bootstrap-4.min.css') }}">
			<link rel="stylesheet" href="{{ asset('plugins/icheck-bootstrap/icheck-bootstrap.min.css') }}">
			<link rel="stylesheet" href="{{ asset('plugins/jqvmap/jqvmap.min.css') }}">
			<link href="https://hayageek.github.io/jQuery-Upload-File/4.0.11/uploadfile.css" rel="stylesheet">
		{% endif %}

		<!-- Notifications -->
		<link rel="stylesheet" href="{{ asset('css/notif.css') }}">
		<link rel="stylesheet" href="{{ asset('bundles/mercuryseriesflashy/css/flashy.css') }}"> {% block stylesheets %}{% endblock %}
		<style></style>
	</head>
	<body>

		<div class="preloader">
			<span class="preloader-spin"></span>
		</div>

		<div class="site">

			<header class="d-flex align-items-center">
				{% block nav %}
					{% include 'accueil/layouts/nav.html.twig' %}
				{% endblock %}
			</header>

			{% block body %}{% endblock %}
			{% block footer %}
				{% include 'accueil/layouts/footer.html.twig' %}
			{% endblock %}

		</div>

		<!-- Core JavaScript Libraries -->
		<script src="{{ asset('assets/js/jquery-2.2.4.min.js') }}"></script>
		<script src="{{ asset('assets/js/vendor/popper.min.js') }}"></script>
		<script src="{{ asset('assets/js/vendor/bootstrap.min.js') }}"></script>

		<!-- Site Core Scripts -->
		<script src="{{ asset('assets/js/vendor/owl.carousel.min.js') }}"></script>
		<script src="{{ asset('assets/js/vendor/isotope.pkgd.min.js') }}"></script>
		<script src="{{ asset('assets/js/vendor/jquery.barfiller.js') }}"></script>
		<script src="{{ asset('assets/js/vendor/loopcounter.js') }}"></script>
		<script src="{{ asset('assets/js/vendor/jquery.slicknav.js') }}"></script>
		<script src="{{ asset('assets/js/active.js') }}"></script>

		<!-- Plugins -->
		<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.10/js/select2.min.js"></script>
		<script src="{{ asset('js/off-canvas.js') }}"></script>
		<script src="{{ asset('js/misc.js') }}"></script>
		<script src="{{ asset('js/notif.js') }}"></script>

		<!-- Admin/Dashboard Scripts (conditionally loaded) -->
		{% if app.request.attributes.get('_route') starts with 'admin' %}
			<script src="https://hayageek.github.io/jQuery-Upload-File/4.0.11/jquery.uploadfile.min.js"></script>
			<script src="{{ asset('plugins/jquery-ui/jquery-ui.min.js') }}"></script>
			<script src="{{ asset('plugins/bootstrap/js/bootstrap.bundle.min.js') }}"></script>
			<script src="{{ asset('plugins/jquery-knob/jquery.knob.min.js') }}"></script>
			<script src="{{ asset('plugins/moment/moment.min.js') }}"></script>
			<script src="{{ asset('plugins/daterangepicker/daterangepicker.js') }}"></script>
			<script src="{{ asset('plugins/tempusdominus-bootstrap-4/js/tempusdominus-bootstrap-4.min.js') }}"></script>
			<script src="{{ asset('plugins/summernote/summernote-bs4.min.js') }}"></script>
			<script src="{{ asset('plugins/overlayScrollbars/js/jquery.overlayScrollbars.min.js') }}"></script>
		{% endif %}

		<!-- Notifications -->
		<script src="{{ asset('bundles/mercuryseriesflashy/js/flashy.js') }}"></script>
		{% block javascripts %}{% endblock %}
		<!-- Include Flashy default partial -->

		{{ include('@MercurySeriesFlashy/flashy.html.twig') }}
	</body>
</html>
