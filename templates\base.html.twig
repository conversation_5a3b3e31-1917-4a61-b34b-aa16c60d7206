<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<meta name='robots' content='index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1' />
		<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
		<title>JOEL LOCATION en Guadeloupe -
			{% block title %}{% endblock %}
		</title>
		<meta name="google-site-verification" content="j2wXTqK0S2jILfO04BGz0vj1NLuQpCEO98nazjfg5As" /> 
		<meta name="google-site-verification" content="Ovl1bjaSIruFHRopNDIYsKQLj3iEzivcO1NQ1lb597A" />
		<meta name="description" content="Agence de Location de voitures basée à le Moule, en Guadeloupe" />
		<link rel="icon" type="image/x-icon" href="../../images/Joel-Location-new.png"/>

		<!-- Required CSS files -->
		{# <link href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,500,500i,700,700i" rel="stylesheet"> #}

		<link rel="stylesheet" href="/assets/css/barfiller.css"> <link rel="stylesheet" href="/assets/css/animate.css">
		<link rel="stylesheet" href="/assets/css/font-awesome.min.css">
		<link rel="stylesheet" href="{{ asset('assets/css/bootstrap.min.css') }}">
		<link rel="stylesheet" href="/assets/css/slicknav.css">
		<link rel="stylesheet" href="{{ asset('assets/css/main.css') }}">
		<link rel="stylesheet" href="/css/style_site.css">
		<link rel="stylesheet" href="/css/nav.css">
		<link href="https://hayageek.github.io/jQuery-Upload-File/4.0.11/uploadfile.css" rel="stylesheet">
		<link href="/dist/css/select2.css" rel="stylesheet"/>
		<link href="/dist/css/bootstrap.min.css" rel="stylesheet"/>
		<link
		rel="stylesheet" href="https://use.fontawesome.com/releases/v5.8.2/css/all.css" integrity="sha384-oS3vJWv+0UjzBfQzYUhtDYW+Pj2yciDJxpsK1OYPAYjqT085Qq/1cq5FLXAZQ7Ay" crossorigin="anonymous">
		{# <link rel="stylesheet" href="/css/style.css"> #}
		<link rel="stylesheet" href="/css/notif.css">
		<link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.10/css/select2.min.css" rel="stylesheet"/>
		<link
		rel="stylesheet" href="/plugins/fontawesome-free/css/all.min.css">
		<!-- Ionicons -->
		<link
		rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css">
		<!-- Tempusdominus Bbootstrap 4 -->
		<link
		rel="stylesheet" href="/plugins/tempusdominus-bootstrap-4/css/tempusdominus-bootstrap-4.min.css">
		<!-- iCheck -->
		<link
		rel="stylesheet" href="/plugins/icheck-bootstrap/icheck-bootstrap.min.css">
		<!-- JQVMap -->
		<link
		rel="stylesheet" href="/plugins/jqvmap/jqvmap.min.css">
		<!-- Theme style -->
		<link
		rel="stylesheet" href="/dist/css/adminlte.min.css">
		<!-- overlayScrollbars -->
		<link
		rel="stylesheet" href="/plugins/overlayScrollbars/css/OverlayScrollbars.min.css">
		<!-- Daterange picker -->
		<link
		rel="stylesheet" href="/plugins/daterangepicker/daterangepicker.css">
		<!-- summernote -->
		<link
		rel="stylesheet" href="/plugins/summernote/summernote-bs4.css">
		<!-- Google Font: Source Sans Pro -->
		<link href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700" rel="stylesheet">
		<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/4.17.47/css/bootstrap-datetimepicker-standalone.min.css" rel="stylesheet">
		<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/4.17.47/css/bootstrap-datetimepicker.min.css" rel="stylesheet">

		<link
		rel="stylesheet" href="https://use.fontawesome.com/releases/v5.8.2/css/all.css">
		{# <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Sofia"> #}
		<link rel="stylesheet" href="/assets/css/owl.carousel.css">
		<link rel="stylesheet" href="{{ asset('bundles/mercuryseriesflashy/css/flashy.css') }}"> {% block stylesheets %}{% endblock %}
		<style></style>
	</head>
	<body>

		<div class="preloader">
			<span class="preloader-spin"></span>
		</div>

		<div class="site">

			<header class="d-flex align-items-center">
				{% block nav %}
					{% include 'accueil/layouts/nav.html.twig' %}
				{% endblock %}
			</header>

			{% block body %}{% endblock %}
			{% block footer %}
				{% include 'accueil/layouts/footer.html.twig' %}
			{% endblock %}

		</div>

		<!--Required JS files-->
		<script src="/assets/js/jquery-2.2.4.min.js"></script>
		<script src="/assets/js/vendor/popper.min.js"></script>
		<script src="/assets/js/vendor/bootstrap.min.js"></script>
		<script src="/assets/js/vendor/owl.carousel.min.js"></script>
		<script src="/assets/js/vendor/isotope.pkgd.min.js"></script>
		<script src="/assets/js/vendor/jquery.barfiller.js"></script>
		<script src="/assets/js/vendor/loopcounter.js"></script>
		<!--<script src="assets/js/vendor/slicknav.min.js"></script>-->
		<script src="/assets/js/vendor/jquery.slicknav.js"></script>
		<script src="/assets/js/active.js"></script>

		<!-- jQuery -->
		<script src="https://hayageek.github.io/jQuery-Upload-File/4.0.11/jquery.uploadfile.min.js"></script>
		<script src="/dist/js/select2.js"></script>
		<script src="/dist/js/up.js"></script>
		<script src="/dist/js/bootstrap.min.js"></script>
		<script src="/dist/js/jquery.uploadfile.js"></script>
		<!-- inject:js -->
		<script src="/js/off-canvas.js"></script>
		<script src="/js/misc.js"></script>
		<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.10/js/select2.min.js"></script>
		<script src="/js/notif.js"></script>
		<!-- jQuery UI 1.11.4 -->
		<script src="/plugins/jquery-ui/jquery-ui.min.js"></script>
		<!-- Resolve conflict in jQuery UI tooltip with Bootstrap tooltip -->
		<!-- <script>$.widget.bridge('uibutton', $.ui.button)</script>-->
		<!-- Bootstrap 4 --><script src="/plugins/bootstrap/js/bootstrap.bundle.min.js"> </script>

		<script src="/plugins/jquery-knob/jquery.knob.min.js"></script>
		<!-- daterangepicker -->
		<script src="/plugins/moment/moment.min.js"></script>
		<script src="/plugins/daterangepicker/daterangepicker.js"></script>
		<!-- Tempusdominus Bootstrap 4 -->
		<script src="/plugins/tempusdominus-bootstrap-4/js/tempusdominus-bootstrap-4.min.js"></script>
		<!-- Summernote -->
		<script src="/plugins/summernote/summernote-bs4.min.js"></script>
		<!-- overlayScrollbars -->
		<script src="/plugins/overlayScrollbars/js/jquery.overlayScrollbars.min.js"></script>

		<script src="{{ asset('bundles/mercuryseriesflashy/js/flashy.js') }}"></script>
		{% block javascripts %}{% endblock %}
		<!-- Include Flashy default partial -->

		{{ include('@MercurySeriesFlashy/flashy.html.twig') }}
	</body>
</html>
